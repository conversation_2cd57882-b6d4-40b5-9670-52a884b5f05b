// Tour Type Verification Utility
// This file helps verify that the tour type system is working correctly

import { Tour } from '@/types/firebase';

// Valid tour types according to our new system
export const VALID_TOUR_TYPES = ['kilimanjaro', 'wildlife', 'cultural'] as const;
export type ValidTourType = typeof VALID_TOUR_TYPES[number];

// Function to validate if a tour type is valid
export function isValidTourType(tourType: string): tourType is ValidTourType {
  return VALID_TOUR_TYPES.includes(tourType as ValidTourType);
}

// Function to get display name for tour type
export function getTourTypeDisplayName(tourType: ValidTourType): string {
  switch (tourType) {
    case 'kilimanjaro':
      return 'Kilimanjaro Climbing';
    case 'wildlife':
      return 'Wildlife Safari';
    case 'cultural':
      return 'Cultural Experience';
    default:
      return 'Unknown Tour Type';
  }
}

// Function to validate a tour object
export function validateTour(tour: Partial<Tour>): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check if tour type is valid
  if (!tour.tourType) {
    errors.push('Tour type is required');
  } else if (!isValidTourType(tour.tourType)) {
    errors.push(`Invalid tour type: ${tour.tourType}. Valid types are: ${VALID_TOUR_TYPES.join(', ')}`);
  }

  // Check other required fields
  if (!tour.title) {
    errors.push('Tour title is required');
  }

  if (!tour.description) {
    errors.push('Tour description is required');
  }

  if (typeof tour.price !== 'number' || tour.price <= 0) {
    errors.push('Tour price must be a positive number');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}



// Function to run verification tests
export function runTourTypeVerificationTests(): { passed: number; failed: number; results: string[] } {
  const results: string[] = [];
  let passed = 0;
  let failed = 0;

  // Test 1: Valid tour types
  VALID_TOUR_TYPES.forEach(tourType => {
    if (isValidTourType(tourType)) {
      results.push(`✅ Valid tour type test passed: ${tourType}`);
      passed++;
    } else {
      results.push(`❌ Valid tour type test failed: ${tourType}`);
      failed++;
    }
  });

  // Test 2: Invalid tour types
  const invalidTypes = ['standard', 'luxury', 'budget', 'ultra', 'invalid'];
  invalidTypes.forEach(tourType => {
    if (!isValidTourType(tourType)) {
      results.push(`✅ Invalid tour type test passed: ${tourType} correctly rejected`);
      passed++;
    } else {
      results.push(`❌ Invalid tour type test failed: ${tourType} incorrectly accepted`);
      failed++;
    }
  });



  return { passed, failed, results };
}

// Export for console testing
export function logVerificationResults(): void {
  console.log('🔍 Running Tour Type Verification Tests...\n');
  const { passed, failed, results } = runTourTypeVerificationTests();
  
  results.forEach(result => console.log(result));
  
  console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);
  
  if (failed === 0) {
    console.log('🎉 All tour type verification tests passed!');
  } else {
    console.log('⚠️ Some tests failed. Please review the tour type system.');
  }
}
