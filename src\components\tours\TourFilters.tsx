
import React, { use<PERSON><PERSON>back, memo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';

interface Filters {
  tourType: string;
  accommodationLevel: string;
  priceRange: number[];
  duration: string;
  destinations: string[];
}

interface TourFiltersProps {
  filters: Filters;
  onFiltersChange: (filters: Filters) => void;
  onClose?: () => void; // For mobile auto-close functionality
}

const TourFilters: React.FC<TourFiltersProps> = memo(({ filters, onFiltersChange, onClose }) => {
  const tourTypes = [
    { value: 'wildlife', label: 'Wildlife Safari' },
    { value: 'cultural', label: 'Cultural Safari' },
    { value: 'kilimanjaro', label: 'Mount Climbing' }
  ];
  const accommodationLevels = ['Budget', 'Mid-range', 'Luxury'];
  const destinations = ['Serengeti', 'Ngorongoro', 'Tarangire', 'Lake Manyara', 'Mount Kilimanjaro', 'Maasai Villages'];

  const handleFilterChange = useCallback((key: keyof Filters, value: any) => {
    onFiltersChange({ ...filters, [key]: value });
    // Auto-close on mobile after filter selection (except for price range which is continuous)
    if (onClose && key !== 'priceRange' && window.innerWidth < 768) {
      setTimeout(() => onClose(), 300); // Small delay for better UX
    }
  }, [filters, onFiltersChange, onClose]);

  const clearFilters = useCallback(() => {
    onFiltersChange({
      tourType: 'all',
      accommodationLevel: 'all',
      priceRange: [0, 10000],
      duration: 'all',
      destinations: []
    });
  }, [onFiltersChange]);

  return (
    <div className="h-full flex flex-col min-h-0">
      {/* Scrollable Content Area */}
      <ScrollArea className="flex-1 px-1 [&>div>div]:!block">
        <div className="space-y-6 pr-3 pb-6">
          {/* Tour Type Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-[#F2EEE6] block">Tour Type</Label>
            <Select value={filters.tourType} onValueChange={(value) => handleFilterChange('tourType', value)}>
              <SelectTrigger className="bg-[#1a1e23] border-[#D4C2A4]/30 text-[#F2EEE6] hover:border-[#D4C2A4] focus:border-[#D4C2A4] transition-all duration-300">
                <SelectValue placeholder="All Tour Types" />
              </SelectTrigger>
              <SelectContent className="bg-[#1a1e23] border-[#D4C2A4]/30">
                <SelectItem value="all" className="text-[#F2EEE6] hover:bg-[#D4C2A4]/10">All Tour Types</SelectItem>
                {tourTypes.map((tourType) => (
                  <SelectItem
                    key={tourType.value}
                    value={tourType.value}
                    className="text-[#F2EEE6] hover:bg-[#D4C2A4]/10"
                  >
                    {tourType.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Accommodation Level */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-[#F2EEE6] block">Accommodation Level</Label>
            <Select value={filters.accommodationLevel} onValueChange={(value) => handleFilterChange('accommodationLevel', value)}>
              <SelectTrigger className="bg-[#1a1e23] border-[#D4C2A4]/30 text-[#F2EEE6] hover:border-[#D4C2A4] focus:border-[#D4C2A4] transition-all duration-300">
                <SelectValue placeholder="All Levels" />
              </SelectTrigger>
              <SelectContent className="bg-[#1a1e23] border-[#D4C2A4]/30">
                <SelectItem value="all" className="text-[#F2EEE6] hover:bg-[#D4C2A4]/10">All Levels</SelectItem>
                {accommodationLevels.map((level) => (
                  <SelectItem
                    key={level}
                    value={level}
                    className="text-[#F2EEE6] hover:bg-[#D4C2A4]/10"
                  >
                    {level}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Price Range */}
          <div className="space-y-3">
            <Label className="text-sm font-medium text-[#F2EEE6] block">
              Price Range: <span className="text-[#D4C2A4]">${filters.priceRange[0]} - ${filters.priceRange[1]}</span>
            </Label>
            <div className="px-2">
              <Slider
                value={filters.priceRange}
                onValueChange={(value) => handleFilterChange('priceRange', value)}
                max={10000}
                min={0}
                step={100}
                className="w-full [&_[role=slider]]:bg-[#D4C2A4] [&_[role=slider]]:border-[#D4C2A4] [&_.bg-primary]:bg-[#D4C2A4]"
              />
            </div>
          </div>

          {/* Duration */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-[#F2EEE6] block">Duration</Label>
            <Select value={filters.duration} onValueChange={(value) => handleFilterChange('duration', value)}>
              <SelectTrigger className="bg-[#1a1e23] border-[#D4C2A4]/30 text-[#F2EEE6] hover:border-[#D4C2A4] focus:border-[#D4C2A4] transition-all duration-300">
                <SelectValue placeholder="Any Duration" />
              </SelectTrigger>
              <SelectContent className="bg-[#1a1e23] border-[#D4C2A4]/30">
                <SelectItem value="all" className="text-[#F2EEE6] hover:bg-[#D4C2A4]/10">Any Duration</SelectItem>
                <SelectItem value="1-3 days" className="text-[#F2EEE6] hover:bg-[#D4C2A4]/10">1-3 days</SelectItem>
                <SelectItem value="4-7 days" className="text-[#F2EEE6] hover:bg-[#D4C2A4]/10">4-7 days</SelectItem>
                <SelectItem value="8+ days" className="text-[#F2EEE6] hover:bg-[#D4C2A4]/10">8+ days</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Destinations */}
          <div className="space-y-3">
            <Label className="text-sm font-medium text-[#F2EEE6] block">Destinations</Label>
            <div className="relative">
              <ScrollArea className="h-40 pr-2">
                <div className="space-y-2">
                  {destinations.map((destination) => (
                    <div key={destination} className="flex items-center space-x-2 group">
                      <Checkbox
                        id={destination}
                        checked={filters.destinations.includes(destination)}
                        onCheckedChange={(checked) => {
                          const newDestinations = checked
                            ? [...filters.destinations, destination]
                            : filters.destinations.filter(d => d !== destination);
                          handleFilterChange('destinations', newDestinations);
                        }}
                        className="border-[#D4C2A4]/30 data-[state=checked]:bg-[#D4C2A4] data-[state=checked]:border-[#D4C2A4] data-[state=checked]:text-[#16191D]"
                      />
                      <Label
                        htmlFor={destination}
                        className="text-sm text-[#F2EEE6] cursor-pointer group-hover:text-[#D4C2A4] transition-colors duration-200"
                      >
                        {destination}
                      </Label>
                    </div>
                  ))}
                </div>
              </ScrollArea>
              {/* Scroll indicator */}
              <div className="absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-t from-[#16191D] to-transparent pointer-events-none" />
            </div>
          </div>

          {/* Clear Filters */}
          <div className="pt-4 border-t border-[#D4C2A4]/10 mt-6">
            <Button
              onClick={clearFilters}
              variant="outline"
              className="w-full bg-transparent border-[#D4C2A4]/30 text-[#D4C2A4] hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4] transition-all duration-300"
            >
              Clear All Filters
            </Button>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
});

export default TourFilters;
