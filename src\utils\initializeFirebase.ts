
import {
  collection,
  doc,
  setDoc,
  addDoc,
  Timestamp,
  writeBatch,
  getDocs
} from 'firebase/firestore';
import { createUserWithEmailAndPassword, updateProfile } from 'firebase/auth';
import { db, auth } from '@/lib/firebase';
import { withFirebaseErrorHandling } from '@/utils/firebaseErrorHandler';

export const initializeFirebaseCollections = async () => {
  console.log('🚀 Starting Firebase collections initialization...');

  return withFirebaseErrorHandling(async () => {
    if (!db) {
      throw new Error('Safari database system is not properly configured. Please contact our technical team.');
    }

    console.log('✅ Firebase connection established');

    const batch = writeBatch(db);
    let operationsCount = 0;

    // No sample data - only initialize empty collections

    // Initialize empty collections
    const collections = [
      'bookings', 'reviews', 'activities', 'guides', 'travelGuides',
      'packingLists', 'wildlifeSightings', 'chatMessages', 'weatherData',
      'virtualTours', 'notifications', 'paymentTransactions', 'contentPages',
      'wishlists', 'tourPackages', 'tourAvailability', 'accommodations',
      'blogPosts', 'contactMessages', 'users'
    ];

    for (const collectionName of collections) {
      const collRef = doc(collection(db, collectionName));
      batch.set(collRef, {
        _placeholder: true,
        createdAt: Timestamp.now(),
        note: `Placeholder document for ${collectionName} collection`
      });
      operationsCount++;
    }

    await batch.commit();
    console.log(`✅ Successfully created ${operationsCount} documents across collections`);

    return {
      success: true,
      message: `Safari database collections initialized successfully! Created ${operationsCount} documents.`
    };
  }, 'Firebase Initialization - Collections');
};

export const createAdminUser = async (email: string, password: string, displayName: string) => {
  return withFirebaseErrorHandling(async () => {
    console.log('👤 Creating admin user...');

    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    await updateProfile(user, { displayName });

    const adminProfile = {
      uid: user.uid,
      email: user.email,
      displayName,
      role: 'admin',
      phone: '+1234567890',
      country: 'Tanzania',
      profileImage: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e',
      preferences: {
        accommodation: 'luxury',
        activities: ['Game Drives', 'Photography', 'Cultural Tours'],
        dietaryRestrictions: [],
        fitnessLevel: 'high',
        photographyInterest: true,
        birdingInterest: true
      },
      loyaltyPoints: 5000,
      pastBookings: [],
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    };

    await setDoc(doc(db, 'users', user.uid), adminProfile);

    console.log('✅ Admin user created successfully!');
    return { success: true, user, profile: adminProfile };
  }, 'Firebase Initialization - Create Admin User');
};
