
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { 
  Package, 
  Clock, 
  MapPin, 
  Star, 
  DollarSign,
  Users,
  Calendar,
  Mountain,
  Camera,
  Percent
} from 'lucide-react';
import { TourPackage, Tour } from '@/types/firebase';
import { FirebaseService } from '@/services/firebase';

interface TourPackageSelectorProps {
  selectedTourId: string;
  onPackageSelect: (packageData: TourPackage | null) => void;
  onPriceUpdate: (price: number) => void;
}

const TourPackageSelector: React.FC<TourPackageSelectorProps> = ({
  selectedTourId,
  onPackageSelect,
  onPriceUpdate
}) => {
  const [availablePackages, setAvailablePackages] = useState<TourPackage[]>([]);
  const [selectedPackage, setSelectedPackage] = useState<TourPackage | null>(null);
  const [packageTours, setPackageTours] = useState<Tour[]>([]);
  const [loading, setLoading] = useState(false);

  // Load packages from Firebase (currently empty - no hardcoded data)
  useEffect(() => {
    // TODO: Load tour packages from Firebase
    setAvailablePackages([]);
  }, [selectedTourId]);

  const handlePackageSelection = (pkg: TourPackage) => {
    if (selectedPackage?.id === pkg.id) {
      setSelectedPackage(null);
      onPackageSelect(null);
      onPriceUpdate(0);
    } else {
      setSelectedPackage(pkg);
      onPackageSelect(pkg);
      onPriceUpdate(pkg.totalPrice);
    }
  };

  const calculateSavings = (pkg: TourPackage) => {
    const originalPrice = pkg.totalPrice / (1 - pkg.discount / 100);
    return originalPrice - pkg.totalPrice;
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          Enhance Your Adventure
        </h3>
        <p className="text-gray-600">
          Combine multiple experiences for the ultimate Tanzania adventure and save money!
        </p>
      </div>

      <div className="grid gap-6">
        {availablePackages.map((pkg) => (
          <Card 
            key={pkg.id}
            className={`cursor-pointer transition-all duration-200 ${
              selectedPackage?.id === pkg.id 
                ? 'ring-2 ring-orange-500 shadow-lg' 
                : 'hover:shadow-lg'
            }`}
            onClick={() => handlePackageSelection(pkg)}
          >
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <Checkbox
                    checked={selectedPackage?.id === pkg.id}
                    onChange={() => {}} // Handled by card click
                    className="mt-1"
                  />
                  <div>
                    <CardTitle className="text-xl mb-2 flex items-center gap-2">
                      <Package className="h-5 w-5 text-orange-600" />
                      {pkg.title}
                      {pkg.featured && (
                        <Badge className="bg-orange-600 text-white">
                          Most Popular
                        </Badge>
                      )}
                    </CardTitle>
                    <p className="text-gray-600 mb-3">{pkg.description}</p>
                    
                    <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        <span>{pkg.totalDuration}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-4 w-4" />
                        <span>{pkg.itinerary.length} Experiences</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="flex items-center gap-2 mb-1">
                    <Badge variant="secondary" className="text-green-700 bg-green-100">
                      <Percent className="h-3 w-3 mr-1" />
                      Save {pkg.discount}%
                    </Badge>
                  </div>
                  <div className="text-2xl font-bold text-orange-600">
                    ${pkg.totalPrice.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-500">
                    Save ${calculateSavings(pkg).toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-400">per person</div>
                </div>
              </div>
            </CardHeader>

            <CardContent className="pt-0">
              {/* Package Itinerary */}
              <div className="mb-4">
                <h4 className="font-semibold text-gray-900 mb-3">Package Itinerary</h4>
                <div className="space-y-3">
                  {pkg.itinerary.map((phase, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center text-orange-600 font-semibold text-sm">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <h5 className="font-medium text-gray-900">{phase.phase}</h5>
                          <span className="text-sm text-gray-500">{phase.duration}</span>
                        </div>
                        <p className="text-sm text-gray-600 mb-1">{phase.description}</p>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>🏨 {phase.accommodation}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <Separator className="my-4" />

              {/* What's Included */}
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">What's Included</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {pkg.includes.map((item, index) => (
                    <div key={index} className="flex items-start space-x-2 text-sm">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                      <span className="text-gray-700">{item}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Package Benefits */}
              {selectedPackage?.id === pkg.id && (
                <div className="mt-4 p-4 bg-orange-50 rounded-lg border border-orange-200">
                  <h5 className="font-semibold text-orange-800 mb-2">Package Benefits</h5>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                    <div className="flex items-center gap-2 text-orange-700">
                      <DollarSign className="h-4 w-4" />
                      <span>Save ${calculateSavings(pkg).toLocaleString()}</span>
                    </div>
                    <div className="flex items-center gap-2 text-orange-700">
                      <Calendar className="h-4 w-4" />
                      <span>Seamless Planning</span>
                    </div>
                    <div className="flex items-center gap-2 text-orange-700">
                      <Star className="h-4 w-4" />
                      <span>Premium Experiences</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {selectedPackage && (
        <Card className="bg-gradient-to-r from-orange-50 to-red-50 border-orange-200">
          <CardContent className="p-6">
            <div className="text-center">
              <h4 className="text-xl font-bold text-gray-900 mb-2">
                🎉 Package Selected: {selectedPackage.title}
              </h4>
              <p className="text-gray-600 mb-4">
                You've saved ${calculateSavings(selectedPackage).toLocaleString()} by choosing this package!
              </p>
              <div className="flex items-center justify-center gap-6 text-sm">
                <div className="text-center">
                  <div className="font-semibold text-gray-900">Total Duration</div>
                  <div className="text-orange-600">{selectedPackage.totalDuration}</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-gray-900">Total Savings</div>
                  <div className="text-green-600">{selectedPackage.discount}% off</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-gray-900">Package Price</div>
                  <div className="text-orange-600 font-bold">${selectedPackage.totalPrice.toLocaleString()}</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default TourPackageSelector;
