
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarIcon, Home, Loader2, Star, Sparkles } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { FirebaseService } from '@/services/firebase';
import { EmailService, CustomTourEmailData } from '@/services/emailService';
import { DestinationService } from '@/services/destinationService';
import { Destination } from '@/types/firebase';
import { format } from 'date-fns';
import '@/styles/tour-builder-luxury.css';

const TourBuilder = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [destinations, setDestinations] = useState<Destination[]>([]);
  const [destinationsLoading, setDestinationsLoading] = useState(true);
  const [destinationsError, setDestinationsError] = useState<string | null>(null);

  const [tourData, setTourData] = useState({
    // Basic Info
    duration:  7,
    participants:  2,
    budget: 100,
    startDate: null as Date | null,

    // Destinations
    destinations: [] as string[],

    // Interests
    interests: [] as string[],

    // Accommodation
    accommodation: 'midrange' as 'budget' | 'midrange' | 'luxury' | 'ultra',

    // Activities
    activities: [] as string[],

    // Special Requirements
    specialRequests: '',
    fitnessLevel: 'moderate' as 'easy' | 'moderate' | 'challenging',
    photographyInterest: false,

    // Contact Info
    name: '',
    email: '',
    phone: ''
  });

  // Fetch destinations from Firebase
  useEffect(() => {
    const fetchDestinations = async () => {
      try {
        setDestinationsLoading(true);
        setDestinationsError(null);
        const fetchedDestinations = await DestinationService.getDestinations();
        setDestinations(fetchedDestinations);
      } catch (error) {
        console.error('Error fetching destinations:', error);
        setDestinationsError('Failed to load destinations. Please try again.');
        // No fallback data - leave destinations empty if Firebase fails
      } finally {
        setDestinationsLoading(false);
      }
    };

    fetchDestinations();
  }, []);

  // Memoize static arrays to prevent unnecessary re-renders
  const interests = useMemo(() => [
    'Big Five Safari',
    'Great Migration',
    'Bird Watching',
    'Photography',
    'Cultural Experiences',
    'Adventure Sports',
    'Relaxation',
    'Conservation Learning'
  ], []);

  const activities = useMemo(() => [
    'Game Drives',
    'Walking Safaris',
    'Hot Air Balloon',
    'Cultural Village Visits',
    'Night Drives',
    'Bush Camping',
    'Photography Workshops',
    'Conservation Activities'
  ], []);

  // Memoize the toggle handler to prevent unnecessary re-renders
  const handleArrayToggle = useCallback((array: string[], item: string, field: keyof typeof tourData) => {
    const newArray = array.includes(item)
      ? array.filter(i => i !== item)
      : [...array, item];

    setTourData(prev => ({
      ...prev,
      [field]: newArray
    }));
  }, []);

       // Helper function to send custom tour email notification
      const sendCustomTourEmailNotification = async (tourData: any, requestId: string) => {
        try {
          const emailData: CustomTourEmailData = {
            customerName: tourData.name,
            customerEmail: tourData.email,
            customerPhone: tourData.phone,
            duration: tourData.duration,
            participants: tourData.participants,
            budget: [tourData.budget || 0], // ✅ FIX: Wrap the budget in an array
            startDate: tourData.startDate ? format(tourData.startDate, 'yyyy-MM-dd') : '',
            destinations: tourData.destinations,
            interests: tourData.interests,
            accommodation: tourData.accommodation,
            activities: tourData.activities,
            specialRequests: tourData.specialRequests,
            fitnessLevel: tourData.fitnessLevel,
            photographyInterest: tourData.photographyInterest,
            requestId
          };
    
          await EmailService.sendCustomTourNotification(emailData);
          console.log('✅ Custom tour email notification sent successfully');
        } catch (error) {
          console.error('❌ Error sending custom tour email notification:', error);
          // Don't throw error to avoid breaking the submission process
        }
      };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!tourData.name || !tourData.email) {
      toast({
        title: "Please fill in your contact information",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Send the tour request to Firebase
      const requestId = await FirebaseService.createCustomTourRequest({
        // FIX: Ensure duration is a number, default to 0 if empty
        duration: tourData.duration || 0,
        // FIX: Ensure participants is a number, default to 0 if empty
        participants: tourData.participants || 0,
        // FIX: Wrap budget in an array and ensure it's a number
        budget: [tourData.budget || 0 ],
        startDate: tourData.startDate ? format(tourData.startDate, 'yyyy-MM-dd') : '',
        destinations: tourData.destinations,
        interests: tourData.interests,
        accommodation: tourData.accommodation,
        activities: tourData.activities,
        specialRequests: tourData.specialRequests,
        fitnessLevel: tourData.fitnessLevel,
        photographyInterest: tourData.photographyInterest,
        name: tourData.name,
        email: tourData.email,
        phone: tourData.phone
      });

      // Send email notification to admin
      await sendCustomTourEmailNotification(tourData, requestId);

      toast({
        title: "Custom Tour Request Sent!",
        description: "We'll contact you within 24 hours with a personalized itinerary."
      });

      // Reset form
      setTourData({
        duration: '',
        participants: '',
        budget: '',
        startDate: null,
        destinations: [],
        interests: [],
        accommodation: 'midrange',
        activities: [],
        specialRequests: '',
        fitnessLevel: 'moderate',
        photographyInterest: false,
        name: '',
        email: '',
        phone: ''
      });

    } catch (error) {
      console.error('Error sending custom tour request:', error);
      toast({
        title: "Failed to send request",
        description: "Please try again or contact us directly.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Luxury section separator component
  const LuxurySectionSeparator = ({ title }: { title: string }) => (
    <div className="relative my-6 sm:my-8 md:my-12">
      <div className="flex items-center justify-center">
        <div className="flex-1 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/30 to-transparent"></div>
        <div className="mx-3 sm:mx-4 md:mx-6">
          <div className="bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-full px-3 sm:px-4 md:px-6 py-1.5 sm:py-2 md:py-3">
            <div className="flex items-center gap-1.5 sm:gap-2">
              <Star className="w-3 h-3 sm:w-3 sm:h-3 md:w-4 md:h-4 text-[#D4C2A4]" />
              <span className="font-open-sans text-xs sm:text-xs md:text-sm text-[#D4C2A4] tracking-wider uppercase font-medium">
                {title}
              </span>
              <Star className="w-3 h-3 sm:w-3 sm:h-3 md:w-4 md:h-4 text-[#D4C2A4]" />
            </div>
          </div>
        </div>
        <div className="flex-1 h-px bg-gradient-to-r from-transparent via-[#D4C2A4]/30 to-transparent"></div>
      </div>
    </div>
  );

  return (
    <div className="tour-builder-page min-h-screen relative bg-gradient-to-br from-[#16191D] via-[#1A1E23] to-[#16191D]" style={{ backgroundColor: '#16191D' }}>
      {/* Luxury background pattern */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-br from-[#D4C2A4]/5 via-transparent to-[#D4C2A4]/5"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(212,194,164,0.1)_0%,transparent_50%)]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_75%_75%,rgba(212,194,164,0.08)_0%,transparent_50%)]"></div>
      </div>

      {/* Home Icon - Top Left */}
      <div className="absolute top-4 left-4 sm:top-6 sm:left-6 z-20">
        <button
          onClick={() => navigate('/')}
          className="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-[#D4C2A4]/10 backdrop-blur-xl border border-[#D4C2A4]/20 rounded-full shadow-2xl hover:bg-[#D4C2A4]/20 hover:border-[#D4C2A4]/40 hover:shadow-3xl transition-all duration-300 group"
          aria-label="Go to Homepage"
        >
          <Home className="w-4 h-4 sm:w-5 sm:h-5 text-[#D4C2A4] group-hover:text-[#F2EEE6] transition-colors" />
        </button>
      </div>

      {/* Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4 sm:p-6 py-8 sm:py-12">
        <div className="w-full max-w-4xl">
          {/* Luxury Header */}
          <div className="text-center mb-6 sm:mb-8 md:mb-12">
            {/* Luxury Badge */}
            <div className="inline-flex items-center gap-1.5 sm:gap-2 bg-[#D4C2A4]/10 backdrop-blur-sm border border-[#D4C2A4]/20 rounded-full px-3 sm:px-4 md:px-6 py-1.5 sm:py-2 mb-4 sm:mb-6">
              <Star className="w-3 h-3 sm:w-4 sm:h-4 text-[#D4C2A4]" />
              <span className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wider uppercase">Premium Safari Experience</span>
            </div>

            <h1 className="font-cormorant text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl text-[#F2EEE6] mb-3 sm:mb-4 leading-tight">
              Design Your
              <span className="block text-[#D4C2A4] italic luxury-glow-text">Dream Safari</span>
            </h1>
            <p className="font-open-sans text-[#F2EEE6]/70 text-sm sm:text-base md:text-lg lg:text-xl max-w-xl sm:max-w-2xl mx-auto leading-relaxed px-2">
              Share your vision with us, and we'll craft an unforgettable safari experience tailored to your dreams.
            </p>
          </div>

          {/* Luxury Form Container */}
          <div className="tour-builder-glass rounded-xl sm:rounded-2xl md:rounded-3xl p-4 sm:p-6 md:p-8 lg:p-12 shadow-2xl max-w-4xl mx-auto">
            <form onSubmit={handleSubmit} className="space-y-6 sm:space-y-8 md:space-y-12">

              {/* Section 1: Basic Information */}
              <div className="luxury-form-section">
                <div className="text-center mb-4 sm:mb-6 md:mb-8">
                  <h2 className="font-cormorant text-xl sm:text-2xl md:text-3xl text-[#F2EEE6] mb-2">
                    Tell us about <em className="italic">yourself</em>
                  </h2>
                  <p className="font-open-sans text-[#F2EEE6]/70 text-xs sm:text-sm px-2">
                    Share your basic travel preferences
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 md:gap-8">
                {/* 1. Duration – input instead of slider */}
<div className="space-y-2">
    <Label
        htmlFor="duration"
        className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wide uppercase"
    >
        Duration (days)
    </Label>
    <Input
        id="duration"
        type="number"
        value={tourData.duration}
        onChange={(e) => {
            const val = e.target.value === '' ? '' : Number(e.target.value);
            setTourData((prev) => ({
                ...prev,
                duration: val,
            }));
        }}
        className="luxury-input h-10 sm:h-11 md:h-12 rounded-lg font-open-sans text-xs sm:text-sm"
        placeholder="e.g., 7"
    />
</div>

{/* 2. Participants – input instead of slider */}
<div className="space-y-2">
    <Label
        htmlFor="participants"
        className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wide uppercase"
    >
        Participants
    </Label>
    <Input
        id="participants"
        type="number"
        value={tourData.participants}
        onChange={(e) => {
            const val = e.target.value === '' ? '' : Number(e.target.value);
            setTourData((prev) => ({
                ...prev,
                participants: val,
            }));
        }}
        className="luxury-input h-10 sm:h-11 md:h-12 rounded-lg font-open-sans text-xs sm:text-sm"
        placeholder="e.g., 2"
    />
</div>

{/* 3. Budget – single number input, mirrored into the array shape */}
<div className="space-y-2">
    <Label
        htmlFor="budget"
        className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wide uppercase"
    >
        Budget (USD)
    </Label>
    <Input
        id="budget"
        type="number"
        value={tourData.budget}
        onChange={(e) => {
            const val = e.target.value === '' ? '' : Number(e.target.value);
            setTourData((prev) => ({
                ...prev,
                budget: val,
            }));
        }}
        className="luxury-input h-10 sm:h-11 md:h-12 rounded-lg font-open-sans text-xs sm:text-sm"
        placeholder="e.g., 5000"
    />
</div>

                  <div className="space-y-2">
                    <Label className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wide uppercase">
                      Preferred Start Date
                    </Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          type="button"
                          variant="outline"
                          className="w-full justify-start text-left font-normal h-10 sm:h-11 md:h-12 bg-[#F2EEE6]/5 border-[#D4C2A4]/30 text-[#F2EEE6] hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/50 rounded-lg text-xs sm:text-sm"
                        >
                          <CalendarIcon className="mr-2 sm:mr-3 h-3 w-3 sm:h-4 sm:w-4 text-[#D4C2A4]" />
                          {tourData.startDate ? (
                            <span className="text-[#F2EEE6]">{format(tourData.startDate, "PPP")}</span>
                          ) : (
                            <span className="text-[#F2EEE6]/50">Pick your departure date</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0 bg-[#16191D] border border-[#D4C2A4]/30 shadow-2xl z-50" align="start">
                        <div className="p-4 border-b border-[#D4C2A4]/20">
                          <h3 className="font-cormorant text-lg font-semibold text-[#F2EEE6] mb-1">
                            Choose Your <em className="italic">Adventure</em> Date
                          </h3>
                          <p className="text-xs text-[#F2EEE6]/70 font-open-sans">
                            Select the perfect time for your safari experience
                          </p>
                        </div>
                        <div className="p-4 tour-builder-calendar">
                          <Calendar
                            mode="single"
                            selected={tourData.startDate || undefined}
                            onSelect={(date) => setTourData(prev => ({ ...prev, startDate: date || null }))}
                            disabled={(date) => {
                              const today = new Date();
                              today.setHours(0, 0, 0, 0);
                              return date < today;
                            }}
                            initialFocus
                            className="rounded-lg tour-builder-calendar text-[#F2EEE6]"
                           
                            
                          />
                        </div>
                        <div className="p-3 border-t border-[#D4C2A4]/20 bg-[#16191D]/50">
                          <div className="flex items-center justify-between text-xs text-[#F2EEE6]/70 font-open-sans">
                            <span className="flex items-center">
                              <div className="w-3 h-3 bg-[#D4C2A4]/30 border border-[#D4C2A4]/50 rounded-full mr-2"></div>
                              Today
                            </span>
                            <span className="flex items-center">
                              <div className="w-3 h-3 bg-[#D4C2A4] rounded-full mr-2"></div>
                              Selected
                            </span>
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </div>

              <LuxurySectionSeparator title="Travel Plans" />

              {/* Section 2: Destinations */}
              <div className="luxury-form-section">
                <div className="text-center mb-4 sm:mb-6 md:mb-8">
                  <h2 className="font-cormorant text-xl sm:text-2xl md:text-3xl text-[#F2EEE6] mb-2">
                    What are your <em className="italic">Travel Plans</em>
                  </h2>
                  <p className="font-open-sans text-[#F2EEE6]/70 text-xs sm:text-sm px-2">
                    You can select more than one destination
                  </p>
                </div>

                {/* Destinations Grid */}
                <div className="mb-4 sm:mb-6">
                  {destinationsLoading ? (
                    <div className="flex justify-center items-center py-6 sm:py-8">
                      <Loader2 className="h-5 w-5 sm:h-6 sm:w-6 animate-spin text-[#D4C2A4]" />
                      <span className="ml-2 text-xs sm:text-sm text-[#F2EEE6]/70">Loading destinations...</span>
                    </div>
                  ) : destinationsError ? (
                    <div className="text-center py-4">
                      <p className="text-red-400 text-xs sm:text-sm mb-2 px-2">{destinationsError}</p>
                      <button
                        type="button"
                        onClick={() => window.location.reload()}
                        className="text-[#D4C2A4] hover:text-[#D4C2A4]/80 text-xs sm:text-sm underline"
                      >
                        Retry
                      </button>
                    </div>
                  ) : (
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-3 md:gap-4">
                      {destinations.map((destination) => (
                        <button
                          key={destination.id}
                          type="button"
                          onClick={() => handleArrayToggle(tourData.destinations, destination.name, 'destinations')}
                          className={`luxury-selection-button px-2 sm:px-3 md:px-4 py-2 sm:py-3 border text-xs sm:text-sm font-open-sans transition-all duration-200 rounded-lg ${
                            tourData.destinations.includes(destination.name)
                              ? 'selected bg-[#D4C2A4]/20 text-[#F2EEE6] border-[#D4C2A4] shadow-lg'
                              : 'bg-[#F2EEE6]/5 text-[#F2EEE6]/70 border-[#D4C2A4]/30 hover:border-[#D4C2A4]/50 hover:bg-[#D4C2A4]/10'
                          }`}
                        >
                          {destination.name}
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                {/* Expert Call Link */}
                <div className="text-center">
                  <a
                    href="tel:+25566121379"
                    className="inline-flex items-center gap-1.5 sm:gap-2 text-[#D4C2A4] hover:text-[#D4C2A4]/80 text-xs sm:text-sm font-open-sans transition-colors"
                  >
                    <Sparkles className="w-3 h-3 sm:w-4 sm:h-4" />
                    Call our Expert for More Explanations
                  </a>
                </div>
              </div>

              <LuxurySectionSeparator title="Interests" />

              {/* Section 3: Interests */}
              <div className="luxury-form-section">
                <div className="text-center mb-4 sm:mb-6 md:mb-8">
                  <h2 className="font-cormorant text-xl sm:text-2xl md:text-3xl text-[#F2EEE6] mb-2">
                    What <em className="italic">interests</em> you most?
                  </h2>
                  <p className="font-open-sans text-[#F2EEE6]/70 text-xs sm:text-sm px-2">
                    Select all that apply
                  </p>
                </div>

                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-3 md:gap-4">
                  {interests.map((interest) => (
                    <button
                      key={interest}
                      type="button"
                      onClick={() => handleArrayToggle(tourData.interests, interest, 'interests')}
                      className={`luxury-selection-button p-2 sm:p-3 md:p-4 border rounded-lg text-xs sm:text-sm font-open-sans transition-all duration-200 ${
                        tourData.interests.includes(interest)
                          ? 'selected bg-[#D4C2A4]/20 text-[#F2EEE6] border-[#D4C2A4] shadow-lg'
                          : 'bg-[#F2EEE6]/5 text-[#F2EEE6]/70 border-[#D4C2A4]/30 hover:border-[#D4C2A4]/50 hover:bg-[#D4C2A4]/10'
                      }`}
                    >
                      {interest}
                    </button>
                  ))}
                </div>
              </div>

              <LuxurySectionSeparator title="Preferences" />

              {/* Section 4: Travel Preferences */}
              <div className="luxury-form-section">
                <div className="text-center mb-4 sm:mb-6 md:mb-8">
                  <h2 className="font-cormorant text-xl sm:text-2xl md:text-3xl text-[#F2EEE6] mb-2">
                    Your <em className="italic">travel</em> preferences
                  </h2>
                  <p className="font-open-sans text-[#F2EEE6]/70 text-xs sm:text-sm px-2">
                    Help us customize your experience
                  </p>
                </div>

                <div className="space-y-4 sm:space-y-6 md:space-y-8">
                  <div>
                    <Label className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wide uppercase mb-3 sm:mb-4 block">
                      Accommodation Level
                    </Label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                      {[
                        { value: 'budget', label: 'Budget', desc: '$300-400/night' },
                        { value: 'midrange', label: 'Mid-range', desc: '$400-600/night' },
                        { value: 'luxury', label: 'Luxury', desc: '$600-1000/night' },
                        { value: 'ultra', label: 'Ultra Luxury', desc: '$1000+/night' }
                      ].map((option) => (
                        <button
                          key={option.value}
                          type="button"
                          onClick={() => setTourData(prev => ({ ...prev, accommodation: option.value as 'budget' | 'midrange' | 'luxury'| 'ultra' }))}
                          className={`luxury-selection-button p-3 sm:p-4 border rounded-lg transition-all duration-200 ${
                            tourData.accommodation === option.value
                              ? 'selected bg-[#D4C2A4]/20 text-[#F2EEE6] border-[#D4C2A4] shadow-lg'
                              : 'bg-[#F2EEE6]/5 text-[#F2EEE6]/70 border-[#D4C2A4]/30 hover:border-[#D4C2A4]/50 hover:bg-[#D4C2A4]/10'
                          }`}
                        >
                          <div className="text-center">
                            <div className="font-semibold text-xs sm:text-sm font-cormorant">{option.label}</div>
                            <div className="text-xs text-[#F2EEE6]/50 font-open-sans mt-1">{option.desc}</div>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wide uppercase mb-3 block">
                      Fitness Level
                    </Label>
                    <Select
                      value={tourData.fitnessLevel}
                      onValueChange={(value) => setTourData(prev => ({ ...prev, fitnessLevel: value as 'easy' | 'moderate' | 'challenging' }))}
                    >
                      <SelectTrigger className="h-10 sm:h-11 md:h-12 bg-[#F2EEE6]/5 border-[#D4C2A4]/30 text-[#F2EEE6] hover:bg-[#D4C2A4]/10 hover:border-[#D4C2A4]/50 rounded-lg text-xs sm:text-sm">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-[#16191D] border-[#D4C2A4]/30">
                        <SelectItem className='text-[#F2EEE6] hover:bg-[#D4C2A4]/20 text-xs sm:text-sm' value="easy">Easy - Minimal walking</SelectItem>
                        <SelectItem className='text-[#F2EEE6] hover:bg-[#D4C2A4]/20 text-xs sm:text-sm' value="moderate">Moderate - Some walking</SelectItem>
                        <SelectItem className='text-[#F2EEE6] hover:bg-[#D4C2A4]/20 text-xs sm:text-sm' value="challenging">Challenging - Extensive walking/hiking</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <LuxurySectionSeparator title="Activities" />

              {/* Section 5: Activities */}
              <div className="luxury-form-section">
                <div className="text-center mb-4 sm:mb-6 md:mb-8">
                  <h2 className="font-cormorant text-xl sm:text-2xl md:text-3xl text-[#F2EEE6] mb-2">
                    What <em className="italic">activities</em> interest you?
                  </h2>
                  <p className="font-open-sans text-[#F2EEE6]/70 text-xs sm:text-sm px-2">
                    Select all that apply
                  </p>
                </div>

                <div className="space-y-4 sm:space-y-6 md:space-y-8">
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-3 md:gap-4">
                    {activities.map((activity) => (
                      <button
                        key={activity}
                        type="button"
                        onClick={() => handleArrayToggle(tourData.activities, activity, 'activities')}
                        className={`luxury-selection-button p-2 sm:p-3 md:p-4 border rounded-lg text-xs sm:text-sm font-open-sans transition-all duration-200 ${
                          tourData.activities.includes(activity)
                            ? 'selected bg-[#D4C2A4]/20 text-[#F2EEE6] border-[#D4C2A4] shadow-lg'
                            : 'bg-[#F2EEE6]/5 text-[#F2EEE6]/70 border-[#D4C2A4]/30 hover:border-[#D4C2A4]/50 hover:bg-[#D4C2A4]/10'
                        }`}
                      >
                        {activity}
                      </button>
                    ))}
                  </div>

                  <div>
                    <Label htmlFor="special" className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wide uppercase mb-3 block">
                      Special Requests
                    </Label>
                    <Textarea
                      id="special"
                      value={tourData.specialRequests}
                      onChange={(e) => setTourData(prev => ({ ...prev, specialRequests: e.target.value }))}
                      placeholder="Any dietary restrictions, mobility requirements, or special requests..."
                      className="luxury-input min-h-[100px] sm:min-h-[120px] rounded-lg font-open-sans resize-none text-xs sm:text-sm"
                      rows={4}
                    />
                  </div>
                </div>
              </div>

              <LuxurySectionSeparator title="Contact Details" />

              {/* Section 6: Contact Information */}
              <div className="luxury-form-section">
                <div className="text-center mb-4 sm:mb-6 md:mb-8">
                  <h2 className="font-cormorant text-xl sm:text-2xl md:text-3xl text-[#F2EEE6] mb-2">
                    Almost <em className="italic">done!</em>
                  </h2>
                  <p className="font-open-sans text-[#F2EEE6]/70 text-xs sm:text-sm px-2">
                    We'll contact you with a personalized itinerary
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 md:gap-8">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wide uppercase">
                      Full Name *
                    </Label>
                    <Input
                      id="name"
                      value={tourData.name}
                      onChange={(e) => setTourData(prev => ({ ...prev, name: e.target.value }))}
                      required
                      className="luxury-input h-10 sm:h-11 md:h-12 rounded-lg font-open-sans text-xs sm:text-sm"
                      placeholder="Your full name"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email" className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wide uppercase">
                      Email Address *
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={tourData.email}
                      onChange={(e) => setTourData(prev => ({ ...prev, email: e.target.value }))}
                      required
                      className="luxury-input h-10 sm:h-11 md:h-12 rounded-lg font-open-sans text-xs sm:text-sm"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="phone" className="font-open-sans text-xs sm:text-sm text-[#D4C2A4] tracking-wide uppercase">
                      Phone Number
                    </Label>
                    <Input
                      id="phone"
                      value={tourData.phone}
                      onChange={(e) => setTourData(prev => ({ ...prev, phone: e.target.value }))}
                      className="luxury-input h-10 sm:h-11 md:h-12 rounded-lg font-open-sans text-xs sm:text-sm"
                      placeholder="+****************"
                    />
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <div className="text-center pt-4 sm:pt-6 md:pt-8">
                <Button
                  type="submit"
                  disabled={isSubmitting || !tourData.name || !tourData.email}
                  className="luxury-submit-button text-[#16191D] font-semibold px-6 sm:px-8 md:px-12 py-2.5 sm:py-3 md:py-4 rounded-lg sm:rounded-xl text-sm sm:text-base md:text-lg disabled:opacity-50 disabled:cursor-not-allowed font-open-sans tracking-wide w-full sm:w-auto"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 animate-spin mr-2" />
                      Sending Request...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                      Send My Dream Safari Request
                    </>
                  )}
                </Button>
              </div>

            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TourBuilder;
